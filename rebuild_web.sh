#!/bin/bash

# 3Pay Global - Web Rebuild Script
# This script rebuilds the Flutter web app with optimized settings for better compatibility

echo "🚀 Starting 3Pay Global web rebuild..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for web with HTML renderer (more compatible than CanvasKit)
echo "🔨 Building Flutter web app with HTML renderer..."
flutter build web \
    --release \
    --dart-define=FLUTTER_WEB_USE_SKIA=false \
    --dart-define=FLUTTER_WEB_AUTO_DETECT=false \
    --dart-define=FLUTTER_WEB_CANVASKIT_URL=https://unpkg.com/canvaskit-wasm@0.39.1/bin/ \
    --verbose

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Build output is in: build/web/"
    echo ""
    echo "🌐 To test locally, run:"
    echo "   cd build/web && python3 -m http.server 8080"
    echo ""
    echo "🐳 To build Docker image, run:"
    echo "   docker build -t 3pay-global-web ."
    echo "   docker run -p 8080:8080 3pay-global-web"
else
    echo "❌ Build failed! Check the error messages above."
    exit 1
fi
