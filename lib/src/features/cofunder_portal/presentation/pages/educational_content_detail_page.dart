import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';

import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/web_compatible_audio_player.dart';
import 'package:pocketbase/pocketbase.dart';

class EducationalContentDetailPage extends StatefulWidget {
  final ContentItemModel contentItem;

  const EducationalContentDetailPage({super.key, required this.contentItem});

  static const String routeName = '/educational-content-detail';

  @override
  State<EducationalContentDetailPage> createState() =>
      _EducationalContentDetailPageState();
}

class _EducationalContentDetailPageState
    extends State<EducationalContentDetailPage> {
  final PocketBaseService _pbService = PocketBaseService();
  bool _isRead = false;
  bool _isLoadingReadStatus = true;
  bool _isUpdatingReadStatus = false;
  String? _coFunderProfileId;

  // Podcast-related state
  List<CoFunderPodcastModel> _associatedPodcasts = [];
  bool _isLoadingPodcasts = false;
  String? _podcastError;

  @override
  void initState() {
    super.initState();
    _checkIfRead();
    _fetchAssociatedPodcasts();
  }

  Future<void> _fetchAssociatedPodcasts() async {
    setState(() {
      _isLoadingPodcasts = true;
      _podcastError = null;
    });

    try {
      final result = await _pbService.client
          .collection('cofunder_podcasts')
          .getFullList(
            filter: 'content_item_slug = "${widget.contentItem.slug}"',
          );

      if (mounted) {
        final podcasts =
            result
                .map((record) => CoFunderPodcastModel.fromRecord(record))
                .toList();

        LoggerService.info(
          'Fetched ${podcasts.length} associated podcasts for slug: ${widget.contentItem.slug}',
        );

        setState(() {
          _associatedPodcasts = podcasts;
          _isLoadingPodcasts = false;
        });
      }
    } catch (e) {
      LoggerService.error('Error fetching associated podcasts', e);
      if (mounted) {
        setState(() {
          _podcastError = 'Failed to load associated podcasts';
          _isLoadingPodcasts = false;
        });
      }
    }
  }

  Future<void> _incrementPodcastPlayCount(CoFunderPodcastModel podcast) async {
    try {
      // First verify the podcast still exists
      await _pbService.client
          .collection('cofunder_podcasts')
          .getOne(podcast.id);

      final currentPlayCount = podcast.playCount ?? 0;
      await _pbService.client
          .collection('cofunder_podcasts')
          .update(podcast.id, body: {'play_count': currentPlayCount + 1});

      // Update local state
      if (mounted) {
        setState(() {
          final index = _associatedPodcasts.indexWhere(
            (p) => p.id == podcast.id,
          );
          if (index != -1) {
            _associatedPodcasts[index] = podcast.copyWith(
              playCount: currentPlayCount + 1,
            );
          }
        });
      }
    } catch (e) {
      LoggerService.error('Error incrementing podcast play count', e);

      // If the podcast doesn't exist anymore, refresh the podcast list
      if (e.toString().contains('404') ||
          e.toString().contains("wasn't found")) {
        LoggerService.info('Podcast not found, refreshing podcast list');
        _fetchAssociatedPodcasts();
      }
    }
  }

  Future<void> _fetchCoFunderProfileId() async {
    if (_pbService.currentUser == null) return;
    try {
      final result = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="${_pbService.currentUser!.id}"');
      if (mounted) {
        setState(() {
          _coFunderProfileId = result.id;
        });
      }
    } catch (e) {
      // ignore: avoid_print
      print("Error fetching co_funder_profile ID: $e");
    }
  }

  Future<void> _checkIfRead() async {
    if (_pbService.currentUser == null) {
      if (mounted) setState(() => _isLoadingReadStatus = false);
      return;
    }
    await _fetchCoFunderProfileId();
    if (_coFunderProfileId == null && mounted) {
      setState(() => _isLoadingReadStatus = false);
      return;
    }

    if (_coFunderProfileId != null) {
      try {
        final profileRecord = await _pbService.client
            .collection('co_funder_profiles')
            .getOne(_coFunderProfileId!, expand: 'read_educational_content');

        final List<dynamic> readArticles =
            profileRecord.expand['read_educational_content'] ?? [];
        if (mounted) {
          setState(() {
            _isRead = readArticles.any(
              (article) =>
                  article is RecordModel && article.id == widget.contentItem.id,
            );
            _isLoadingReadStatus = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingReadStatus = false;
          });
        }
        // ignore: avoid_print
        print("Error checking read status: $e");
      }
    } else {
      if (mounted) setState(() => _isLoadingReadStatus = false);
    }
  }

  Future<void> _toggleReadStatus() async {
    if (_pbService.currentUser == null ||
        _coFunderProfileId == null ||
        _isUpdatingReadStatus)
      return;

    setState(() => _isUpdatingReadStatus = true);

    try {
      final currentProfile = await _pbService.client
          .collection('co_funder_profiles')
          .getOne(_coFunderProfileId!, expand: 'read_educational_content');

      List<String> currentReadIds =
          (currentProfile.expand['read_educational_content'] as List<dynamic>?)
              ?.map((e) => (e as RecordModel).id)
              .toList() ??
          <String>[];

      final articleId = widget.contentItem.id;
      bool newReadState;

      if (currentReadIds.contains(articleId)) {
        currentReadIds.remove(articleId);
        newReadState = false;
      } else {
        currentReadIds.add(articleId);
        newReadState = true;
      }

      await _pbService.client
          .collection('co_funder_profiles')
          .update(
            _coFunderProfileId!,
            body: {'read_educational_content': currentReadIds},
          );

      if (mounted) {
        setState(() {
          _isRead = newReadState;
        });
        ShadToaster.of(context).show(
          ShadToast(
            title: Text(newReadState ? 'Marked as Read' : 'Marked as Unread'),
            description: Text(widget.contentItem.title),
          ),
        );
      }
    } catch (e) {
      // ignore: avoid_print
      print("Error updating read status: $e");
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: const Text('Could not update read status.'),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUpdatingReadStatus = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final dateFormat = DateFormat.yMMMd();

    return Scaffold(
      appBar: AppBar(
        title: SizedBox(
          height: kToolbarHeight - 16, // Adjust height as needed
          child: Image.asset('assets/images/logo.png', fit: BoxFit.contain),
        ),
        backgroundColor: theme.colorScheme.accentForeground,
        iconTheme: IconThemeData(color: theme.colorScheme.primaryForeground),
        elevation: 2, // Added subtle elevation
        actions: [
          // Add share button
          // IconButton(
          //   icon: const Icon(LucideIcons.share2),
          //   onPressed: () {
          //     // Implement share functionality
          //     ScaffoldMessenger.of(context).showSnackBar(
          //       const SnackBar(content: Text('Share functionality coming soon')),
          //     );
          //   },
          // ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          // Add subtle background pattern or gradient
          color: theme.colorScheme.background,
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 80.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title with larger font (for better readability)
              Text(
                widget.contentItem.title,
                style: theme.textTheme.h3.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.foreground,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 16),

              // Metadata Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.card,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Published Date with icon
                    if (widget.contentItem.publishedAt != null)
                      Row(
                        children: [
                          Icon(
                            LucideIcons.calendar,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Published: ${dateFormat.format(widget.contentItem.publishedAt!)}',
                            style: theme.textTheme.small.copyWith(
                              color: theme.colorScheme.mutedForeground,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: 12),

                    // Tags with improved styling
                    if (widget.contentItem.tags.isNotEmpty) ...[
                      Row(
                        children: [
                          Icon(
                            LucideIcons.tag,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Tags:',
                            style: theme.textTheme.small.copyWith(
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.mutedForeground,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 8.0,
                        children:
                            widget.contentItem.tags
                                .map(
                                  (tag) => ShadBadge.outline(
                                    // Changed to constructor
                                    backgroundColor: theme.colorScheme.primary
                                        .withOpacity(
                                          0.1,
                                        ), // Changed to constructor
                                    child: Text(
                                      tag,
                                      style: theme.textTheme.small.copyWith(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                      ),
                    ],

                    if (widget.contentItem.tags.isNotEmpty &&
                        widget.contentItem.targetUserLevels.isNotEmpty)
                      const SizedBox(height: 12),

                    // Target User Levels with improved styling
                    if (widget.contentItem.targetUserLevels.isNotEmpty) ...[
                      Row(
                        children: [
                          Icon(
                            LucideIcons.users,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Accessible for Levels:',
                            style: theme.textTheme.small.copyWith(
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.mutedForeground,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 8.0,
                        children:
                            widget.contentItem.targetUserLevels
                                .map(
                                  (level) => ShadBadge.secondary(
                                    // Changed to constructor
                                    child: Text(
                                      level.toString(),
                                      style: theme.textTheme.small.copyWith(
                                        color:
                                            theme
                                                .colorScheme
                                                .secondaryForeground,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                      ),
                    ],

                    // Read status indicator
                    if (_pbService.isSignedIn &&
                        !_isLoadingReadStatus &&
                        _isRead) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(
                            LucideIcons.check, // Changed to check
                            size: 16,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'You have read this content',
                            style: theme.textTheme.small.copyWith(
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Thumbnail Image with improved styling
              Hero(
                tag: 'content_thumbnail_${widget.contentItem.id}',
                child: Container(
                  height: 220,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  clipBehavior:
                      Clip.antiAlias, // Ensures the image respects the border radius
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      (widget.contentItem.thumbnailImage != null &&
                              widget.contentItem.thumbnailImage!.isNotEmpty &&
                              widget.contentItem.getImageUrl(
                                    _pbService.client,
                                  ) !=
                                  null)
                          ? Image.network(
                            widget.contentItem.getImageUrl(_pbService.client)!,
                            fit: BoxFit.cover,
                            loadingBuilder: (
                              BuildContext context,
                              Widget child,
                              ImageChunkEvent? loadingProgress,
                            ) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              );
                            },
                            errorBuilder: (
                              BuildContext context,
                              Object exception,
                              StackTrace? stackTrace,
                            ) {
                              return Icon(
                                LucideIcons.imageOff,
                                size: 48,
                                color: theme.colorScheme.mutedForeground
                                    .withOpacity(0.5),
                              );
                            },
                          )
                          : Icon(
                            LucideIcons.image,
                            size: 48,
                            color: theme.colorScheme.mutedForeground
                                .withOpacity(0.5),
                          ),
                      // Optional overlay for better contrast
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.1),
                            ],
                          ),
                        ),
                      ),
                      // Content type badge
                      Positioned(
                        top: 12,
                        right: 12,
                        child: ShadBadge(
                          // Removed variant
                          backgroundColor: theme.colorScheme.primary,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getContentTypeIcon(widget.contentItem.type),
                                size: 14,
                                color: theme.colorScheme.primaryForeground,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _formatContentType(widget.contentItem.type),
                                style: theme.textTheme.small.copyWith(
                                  color: theme.colorScheme.primaryForeground,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Content-Specific Display with improved styling
              _buildContentSpecificSection(context, widget.contentItem, theme),

              // Associated Podcasts Section
              if (_associatedPodcasts.isNotEmpty) ...[
                const SizedBox(height: 32),
                _buildAssociatedPodcastsSection(theme),
              ],
            ],
          ),
        ),
      ),
      bottomNavigationBar:
          (_pbService.isSignedIn && _coFunderProfileId != null)
              ? Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.background,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ShadButton(
                    // Removed variant
                    width: double.infinity,
                    onPressed:
                        _isLoadingReadStatus || _isUpdatingReadStatus
                            ? null
                            : _toggleReadStatus,
                    child:
                        _isLoadingReadStatus || _isUpdatingReadStatus
                            ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.primaryForeground,
                                ),
                              ),
                            )
                            : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _isRead
                                      ? LucideIcons.check
                                      : LucideIcons.bookOpen,
                                  color:
                                      _isRead
                                          ? Colors
                                              .greenAccent
                                              .shade700 // Vibrant green for check on default button
                                          : theme.colorScheme.primaryForeground,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _isRead ? 'Marked as Read' : 'Mark as Read',
                                  style: TextStyle(
                                    // Text color always primaryForeground for default button
                                    color: theme.colorScheme.primaryForeground,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                  ),
                ),
              )
              : null,
    );
  }

  Widget _buildContentSpecificSection(
    BuildContext context,
    ContentItemModel item,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (item.type == 'blog' ||
              item.type == 'educational_module' ||
              item.type == 'newsletter' ||
              item.type == 'resource') ...[
            // Content body with improved typography
            Html(
              data:
                  item.bodyContent ??
                  item.summary ??
                  '<p>No content available.</p>',
              style: {
                "body": Style(
                  fontSize: FontSize(16),
                  color: theme.colorScheme.foreground,
                  lineHeight: LineHeight(1.8),
                ),
                "p": Style(
                  fontSize: FontSize(16),
                  color: theme.colorScheme.foreground,
                  lineHeight: LineHeight(1.8),
                ),
                // Add more styles for other HTML elements as needed (h1, h2, a, li, etc.)
                "h1": Style(
                  fontSize: FontSize(24),
                  fontWeight: FontWeight.bold,
                ),
                "h2": Style(
                  fontSize: FontSize(20),
                  fontWeight: FontWeight.bold,
                ),
                "a": Style(
                  color: theme.colorScheme.primary,
                  textDecoration: TextDecoration.none,
                ),
              },
              onLinkTap: (url, _, __) {
                // Handle link taps, e.g., launch URL
                // You might need to add url_launcher package for this
                // For now, just print the URL
                // ignore: avoid_print
                print('Tapped link: $url');
              },
            ),
          ],
          if (item.type == 'podcast') ...[
            if (item.summary != null && item.summary!.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: theme.colorScheme.border, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.fileText,
                          size: 18,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Summary',
                          style: theme.textTheme.large.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    SelectableText(
                      item.summary!,
                      style: theme.textTheme.p.copyWith(
                        height: 1.6,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
            ],
            if (item.mediaUrl != null && item.mediaUrl!.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.primary.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: WebCompatibleAudioPlayer(
                  audioUrl: item.mediaUrl!,
                  title: item.title,
                  onPlayCountIncrement: () {
                    // Could implement play count tracking for main content items here
                    LoggerService.info(
                      'Playing main content audio: ${item.title}',
                    );
                  },
                ),
              ),
              const SizedBox(height: 24),
            ],
            if (item.bodyContent != null && item.bodyContent!.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.card,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: theme.colorScheme.border, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.fileText,
                          size: 18,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Show Notes / Transcript',
                          style: theme.textTheme.large.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SelectableText(
                      item.bodyContent!,
                      style: theme.textTheme.p.copyWith(
                        height: 1.8,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildAssociatedPodcastsSection(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  LucideIcons.headphones,
                  size: 20,
                  color: Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Associated Podcasts',
                style: theme.textTheme.h3.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Listen to podcast episodes related to this educational content.',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 20),

          // Podcasts List
          if (_isLoadingPodcasts)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_podcastError != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.destructive.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.circleAlert,
                    size: 20,
                    color: theme.colorScheme.destructive,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _podcastError!,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.destructive,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            Column(
              children:
                  _associatedPodcasts.map((podcast) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildPodcastItem(podcast, theme),
                    );
                  }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildPodcastItem(CoFunderPodcastModel podcast, ShadThemeData theme) {
    final audioUrl = _getPodcastAudioUrl(podcast);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Podcast Header (Episode badge only)
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      LucideIcons.headphones,
                      size: 12,
                      color: Colors.purple,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      podcast.episodeNumber != null
                          ? 'Episode ${podcast.episodeNumber}'
                          : 'Podcast',
                      style: theme.textTheme.small.copyWith(
                        color: Colors.purple,
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (podcast.hasGuestSpeakers) ...[
            const SizedBox(height: 8),
            Text(
              podcast.guestSpeakersText,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Podcast Player
          if (audioUrl != null)
            WebCompatibleAudioPlayer(
              audioUrl: audioUrl,
              title: podcast.getEpisodeTitle(widget.contentItem.title),
              onPlayCountIncrement: () {
                // Only increment if podcast ID is valid
                if (podcast.id.isNotEmpty) {
                  _incrementPodcastPlayCount(podcast);
                }
              },
            )
          else
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.muted.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.circleAlert,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Audio file not available',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  String? _getPodcastAudioUrl(CoFunderPodcastModel podcast) {
    if (podcast.audioFile == null || podcast.audioFile!.isEmpty) {
      return null;
    }

    try {
      return Uri.parse(
        '${_pbService.client.baseURL}/api/files/${podcast.collectionId}/${podcast.id}/${podcast.audioFile}',
      ).toString();
    } catch (e) {
      LoggerService.error('Error constructing podcast audio URL', e);
      return null;
    }
  }

  // Helper method to get icon based on content type
  IconData _getContentTypeIcon(String type) {
    switch (type) {
      case 'blog':
        return LucideIcons.fileText;
      case 'podcast':
        return LucideIcons.headphones;
      case 'educational_module':
        return LucideIcons.graduationCap;
      case 'newsletter':
        return LucideIcons.mail;
      case 'resource':
        return LucideIcons.fileStack;
      default:
        return LucideIcons.file;
    }
  }

  // Helper method to format content type for display
  String _formatContentType(String type) {
    switch (type) {
      case 'blog':
        return 'Blog';
      case 'podcast':
        return 'Podcast';
      case 'educational_module':
        return 'Module';
      case 'newsletter':
        return 'Newsletter';
      case 'resource':
        return 'Resource';
      default:
        return type
            .split('_')
            .map(
              (word) =>
                  word.isNotEmpty
                      ? word[0].toUpperCase() + word.substring(1)
                      : '',
            )
            .join(' ');
    }
  }
}
