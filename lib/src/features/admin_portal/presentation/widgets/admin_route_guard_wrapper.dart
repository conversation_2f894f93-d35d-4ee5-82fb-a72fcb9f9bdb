import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/navigation_guard_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/routes/admin_routes.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_error_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/admin_loading_widget.dart';

/// Wrapper widget that applies route guards to admin pages
class AdminRouteGuardWrapper extends ConsumerStatefulWidget {
  final String route;
  final Widget child;
  final Map<String, String>? parameters;

  const AdminRouteGuardWrapper({
    super.key,
    required this.route,
    required this.child,
    this.parameters,
  });

  @override
  ConsumerState<AdminRouteGuardWrapper> createState() =>
      _AdminRouteGuardWrapperState();
}

class _AdminRouteGuardWrapperState
    extends ConsumerState<AdminRouteGuardWrapper> {
  final NavigationGuardService _guardService = NavigationGuardService();
  bool _isChecking = true;
  bool _hasAccess = false;
  String? _errorMessage;
  String? _redirectRoute;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  @override
  void didUpdateWidget(AdminRouteGuardWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Re-check access if route or parameters changed
    if (oldWidget.route != widget.route ||
        oldWidget.parameters != widget.parameters) {
      _checkAccess();
    }
  }

  Future<void> _checkAccess() async {
    if (!mounted) return;

    setState(() {
      _isChecking = true;
      _hasAccess = false;
      _errorMessage = null;
      _redirectRoute = null;
    });

    try {
      final authState = ref.read(adminAuthProvider);
      final currentUser = authState.adminUser;

      LoggerService.info('Checking route access: ${widget.route}');

      final result = await _guardService.canActivate(
        widget.route,
        currentUser,
        parameters: widget.parameters,
      );

      // Log the navigation attempt
      await _guardService.logNavigationAttempt(
        route: widget.route,
        user: currentUser,
        allowed: result.isAllowed,
        reason: result.reason,
        parameters: widget.parameters,
      );

      if (!mounted) return;

      if (result.isAllowed) {
        setState(() {
          _isChecking = false;
          _hasAccess = true;
        });
      } else {
        setState(() {
          _isChecking = false;
          _hasAccess = false;
          _errorMessage = result.reason;
          _redirectRoute = result.redirectRoute;
        });

        // Handle redirect if specified
        if (result.redirectRoute != null) {
          _handleRedirect(result.redirectRoute!);
        }
      }
    } catch (e) {
      LoggerService.error('Error checking route access: ${widget.route}', e);

      if (!mounted) return;

      setState(() {
        _isChecking = false;
        _hasAccess = false;
        _errorMessage = 'Access check failed';
        _redirectRoute = AdminRoutes.dashboard;
      });
    }
  }

  void _handleRedirect(String redirectRoute) {
    // Use post frame callback to avoid navigation during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        LoggerService.info(
          'Redirecting from ${widget.route} to $redirectRoute',
        );

        if (redirectRoute == AdminRoutes.login) {
          // Replace current route with login
          Navigator.of(context).pushReplacementNamed(redirectRoute);
        } else {
          // Navigate to redirect route
          Navigator.of(
            context,
          ).pushNamedAndRemoveUntil(redirectRoute, (route) => false);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes
    ref.listen<AdminAuthState>(adminAuthProvider, (previous, next) {
      // Re-check access when auth state changes
      if (previous != next) {
        _checkAccess();
      }
    });

    if (_isChecking) {
      return const AdminLoadingWidget(message: 'Verifying access...');
    }

    if (!_hasAccess) {
      return AdminUnauthorizedPage(
        route: widget.route,
        reason: _errorMessage ?? 'Access denied',
        onRetry: _checkAccess,
      );
    }

    return widget.child;
  }
}

/// Page displayed when user is not authorized to access a route
class AdminUnauthorizedPage extends StatelessWidget {
  final String route;
  final String reason;
  final VoidCallback? onRetry;

  const AdminUnauthorizedPage({
    super.key,
    required this.route,
    required this.reason,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return AdminErrorPage(
      title: 'Access Denied',
      message: reason,
      details: 'You do not have permission to access this page: $route',
      icon: Icons.lock,
      actions: [
        if (onRetry != null)
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        TextButton.icon(
          onPressed: () {
            Navigator.of(
              context,
            ).pushNamedAndRemoveUntil(AdminRoutes.dashboard, (route) => false);
          },
          icon: const Icon(Icons.home),
          label: const Text('Go to Dashboard'),
        ),
      ],
    );
  }
}
