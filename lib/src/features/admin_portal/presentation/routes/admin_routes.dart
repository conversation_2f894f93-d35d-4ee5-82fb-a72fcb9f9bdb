import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_login_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/user_management_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_profile_settings_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_settings_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/admin_route_guard_wrapper.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_error_page.dart';

/// Admin portal route definitions and configuration
class AdminRoutes {
  // Route constants
  static const String login = '/admin/login';
  static const String dashboard = '/admin/dashboard';
  static const String userManagement = '/admin/users';
  static const String userDetail = '/admin/users/:userId';
  static const String contentManagement = '/admin/content';
  static const String blogManagement = '/admin/content/blogs';
  static const String podcastManagement = '/admin/content/podcasts';
  static const String analytics = '/admin/analytics';
  static const String notifications = '/admin/notifications';
  static const String auditLogs = '/admin/audit-logs';
  static const String settings = '/admin/settings';
  static const String profile = '/admin/profile';
  static const String systemHealth = '/admin/system-health';

  // Route builders
  static final Map<String, WidgetBuilder> _routes = {
    login: (context) => const AdminLoginPage(),
    dashboard: (context) => const AdminDashboardPage(),
    userManagement: (context) => const UserManagementPage(),
    profile: (context) => const AdminProfileSettingsPage(),
    settings: (context) => const AdminSettingsPage(),
    // TODO: Add other pages as they are implemented
    // contentManagement: (context) => const ContentManagementPage(),
    // analytics: (context) => const AnalyticsPage(),
    // auditLogs: (context) => const AuditLogPage(),
    // systemHealth: (context) => const SystemHealthPage(),
  };

  // Route permissions mapping
  static final Map<String, List<String>> _routePermissions = {
    dashboard: [], // Basic admin access
    userManagement: ['user_management'],
    contentManagement: ['content_management'],
    blogManagement: ['content_management'],
    podcastManagement: ['content_management'],
    analytics: ['analytics_access'],
    notifications: ['notification_management'],
    auditLogs: ['audit_access'],
    settings: ['system_admin'],
    systemHealth: ['system_admin'],
  };

  // Route metadata for breadcrumbs
  static final Map<String, RouteMetadata> _routeMetadata = {
    login: RouteMetadata(title: 'Login', showInBreadcrumb: false),
    dashboard: RouteMetadata(title: 'Dashboard', icon: Icons.dashboard),
    userManagement: RouteMetadata(title: 'User Management', icon: Icons.people),
    userDetail: RouteMetadata(title: 'User Details', parent: userManagement),
    contentManagement: RouteMetadata(
      title: 'Content Management',
      icon: Icons.content_copy,
    ),
    blogManagement: RouteMetadata(
      title: 'Blog Management',
      parent: contentManagement,
    ),
    podcastManagement: RouteMetadata(
      title: 'Podcast Management',
      parent: contentManagement,
    ),
    analytics: RouteMetadata(title: 'Analytics', icon: Icons.analytics),
    notifications: RouteMetadata(
      title: 'Notifications',
      icon: Icons.notifications,
    ),
    auditLogs: RouteMetadata(title: 'Audit Logs', icon: Icons.history),
    settings: RouteMetadata(title: 'Settings', icon: Icons.settings),
    profile: RouteMetadata(title: 'Profile & Settings', icon: Icons.person),
    systemHealth: RouteMetadata(
      title: 'System Health',
      icon: Icons.health_and_safety,
    ),
  };

  /// Generate route for the given settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name;

    LoggerService.info('Generating admin route: $routeName');

    // Handle login route (no guard needed)
    if (routeName == login) {
      return MaterialPageRoute(builder: _routes[login]!, settings: settings);
    }

    // Check if route exists
    if (!_routes.containsKey(routeName)) {
      LoggerService.warning('Admin route not found: $routeName');
      return _errorRoute('Route not found: $routeName');
    }

    // Apply route guard for protected routes
    return MaterialPageRoute(
      builder:
          (context) => AdminRouteGuardWrapper(
            route: routeName!,
            child: _routes[routeName]!(context),
          ),
      settings: settings,
    );
  }

  /// Create error route
  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder:
          (context) => AdminErrorPage(
            title: 'Route Error',
            message: message,
            showRetry: false,
          ),
    );
  }

  /// Get route permissions
  static List<String> getRoutePermissions(String route) {
    return _routePermissions[route] ?? [];
  }

  /// Get route metadata
  static RouteMetadata? getRouteMetadata(String route) {
    return _routeMetadata[route];
  }

  /// Get all available routes
  static List<String> get availableRoutes => _routes.keys.toList();

  /// Check if route exists
  static bool routeExists(String route) {
    return _routes.containsKey(route);
  }

  /// Get route title
  static String getRouteTitle(String route) {
    return _routeMetadata[route]?.title ?? 'Unknown Page';
  }

  /// Build navigation URL with parameters
  static String buildRouteWithParams(String route, Map<String, String> params) {
    String finalRoute = route;
    params.forEach((key, value) {
      finalRoute = finalRoute.replaceAll(':$key', value);
    });
    return finalRoute;
  }

  /// Extract route parameters from URL
  static Map<String, String> extractRouteParams(
    String template,
    String actualRoute,
  ) {
    final Map<String, String> params = {};

    final templateParts = template.split('/');
    final actualParts = actualRoute.split('/');

    if (templateParts.length != actualParts.length) {
      return params;
    }

    for (int i = 0; i < templateParts.length; i++) {
      final templatePart = templateParts[i];
      final actualPart = actualParts[i];

      if (templatePart.startsWith(':')) {
        final paramName = templatePart.substring(1);
        params[paramName] = actualPart;
      }
    }

    return params;
  }

  /// Get parent route for breadcrumb navigation
  static String? getParentRoute(String route) {
    final metadata = _routeMetadata[route];
    return metadata?.parent;
  }

  /// Generate breadcrumb trail for a route
  static List<BreadcrumbItem> generateBreadcrumbs(String currentRoute) {
    final List<BreadcrumbItem> breadcrumbs = [];
    String? route = currentRoute;

    while (route != null) {
      final metadata = _routeMetadata[route];
      if (metadata != null && metadata.showInBreadcrumb) {
        breadcrumbs.insert(
          0,
          BreadcrumbItem(
            title: metadata.title,
            route: route,
            icon: metadata.icon,
          ),
        );
      }
      route = metadata?.parent;
    }

    return breadcrumbs;
  }
}

/// Route metadata for navigation and breadcrumbs
class RouteMetadata {
  final String title;
  final IconData? icon;
  final String? parent;
  final bool showInBreadcrumb;
  final String? description;

  const RouteMetadata({
    required this.title,
    this.icon,
    this.parent,
    this.showInBreadcrumb = true,
    this.description,
  });
}

/// Breadcrumb item for navigation
class BreadcrumbItem {
  final String title;
  final String route;
  final IconData? icon;
  final Map<String, String>? parameters;

  const BreadcrumbItem({
    required this.title,
    required this.route,
    this.icon,
    this.parameters,
  });

  BreadcrumbItem copyWith({
    String? title,
    String? route,
    IconData? icon,
    Map<String, String>? parameters,
  }) {
    return BreadcrumbItem(
      title: title ?? this.title,
      route: route ?? this.route,
      icon: icon ?? this.icon,
      parameters: parameters ?? this.parameters,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BreadcrumbItem &&
        other.title == title &&
        other.route == route &&
        other.icon == icon;
  }

  @override
  int get hashCode {
    return title.hashCode ^ route.hashCode ^ icon.hashCode;
  }

  @override
  String toString() {
    return 'BreadcrumbItem(title: $title, route: $route)';
  }
}
