import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/realtime_update_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_model.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/responsive_layout.dart';

/// Admin profile and settings page
class AdminProfileSettingsPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/profile';

  const AdminProfileSettingsPage({super.key});

  @override
  ConsumerState<AdminProfileSettingsPage> createState() =>
      _AdminProfileSettingsPageState();
}

class _AdminProfileSettingsPageState
    extends ConsumerState<AdminProfileSettingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Profile form controllers
  final _displayNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  // Password form controllers
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Form keys
  final _profileFormKey = GlobalKey<ShadFormState>();
  final _passwordFormKey = GlobalKey<ShadFormState>();

  bool _isUpdatingProfile = false;
  bool _isChangingPassword = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeFormData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _displayNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _initializeFormData() {
    final adminUser = ref.read(adminAuthProvider).adminUser;
    if (adminUser != null) {
      _displayNameController.text = adminUser.displayName;
      _emailController.text = adminUser.email;
      // Note: AdminUser model doesn't have phoneNumber, so we'll leave it empty
      _phoneController.text = '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(adminAuthProvider);
    final adminUser = authState.adminUser;

    if (adminUser == null) {
      return const Scaffold(body: Center(child: Text('No admin user found')));
    }

    return Scaffold(
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(theme, adminUser),
        tablet: _buildTabletLayout(theme, adminUser),
        desktop: _buildDesktopLayout(theme, adminUser),
      ),
    );
  }

  Widget _buildMobileLayout(ShadThemeData theme, AdminUser adminUser) {
    return Column(
      children: [
        _buildHeader(theme, adminUser),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildProfileTab(theme, adminUser),
              _buildPasswordTab(theme),
              _buildSettingsTab(theme),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(ShadThemeData theme, AdminUser adminUser) {
    return _buildMobileLayout(theme, adminUser);
  }

  Widget _buildDesktopLayout(ShadThemeData theme, AdminUser adminUser) {
    return Row(
      children: [
        // Left sidebar with profile info
        Container(
          width: 300,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.card,
            border: Border(right: BorderSide(color: theme.colorScheme.border)),
          ),
          child: _buildProfileSidebar(theme, adminUser),
        ),
        // Main content area
        Expanded(
          child: Column(
            children: [
              _buildTabBar(theme),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildProfileTab(theme, adminUser),
                    _buildPasswordTab(theme),
                    _buildSettingsTab(theme),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(ShadThemeData theme, AdminUser adminUser) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 32,
                backgroundColor: theme.colorScheme.primary,
                backgroundImage:
                    adminUser.avatarUrl != null
                        ? NetworkImage(adminUser.avatarUrl!)
                        : null,
                child:
                    adminUser.avatarUrl == null
                        ? Text(
                          adminUser.initials,
                          style: TextStyle(
                            color: theme.colorScheme.primaryForeground,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      adminUser.displayName,
                      style: theme.textTheme.h3.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      adminUser.email,
                      style: theme.textTheme.p.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        adminUser.permissionLevel.displayName,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildTabBar(theme),
        ],
      ),
    );
  }

  Widget _buildTabBar(ShadThemeData theme) {
    return TabBar(
      controller: _tabController,
      tabs: const [
        Tab(icon: Icon(lucide.LucideIcons.user), text: 'Profile'),
        Tab(icon: Icon(lucide.LucideIcons.lock), text: 'Password'),
        Tab(icon: Icon(lucide.LucideIcons.settings), text: 'Settings'),
      ],
    );
  }

  Widget _buildProfileSidebar(ShadThemeData theme, AdminUser adminUser) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: CircleAvatar(
            radius: 48,
            backgroundColor: theme.colorScheme.primary,
            backgroundImage:
                adminUser.avatarUrl != null
                    ? NetworkImage(adminUser.avatarUrl!)
                    : null,
            child:
                adminUser.avatarUrl == null
                    ? Text(
                      adminUser.initials,
                      style: TextStyle(
                        color: theme.colorScheme.primaryForeground,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                    : null,
          ),
        ),
        const SizedBox(height: 16),
        Center(
          child: Text(
            adminUser.displayName,
            style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: Text(
            adminUser.email,
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),
        Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              adminUser.permissionLevel.displayName,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(height: 32),
        _buildProfileStats(theme, adminUser),
      ],
    );
  }

  Widget _buildProfileStats(ShadThemeData theme, AdminUser adminUser) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Information',
          style: theme.textTheme.large.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 16),
        _buildStatItem(
          theme,
          icon: lucide.LucideIcons.calendar,
          label: 'Member Since',
          value: _formatDate(adminUser.created),
        ),
        const SizedBox(height: 12),
        _buildStatItem(
          theme,
          icon: lucide.LucideIcons.clock,
          label: 'Last Login',
          value: _formatDate(adminUser.lastLogin),
        ),
        const SizedBox(height: 12),
        _buildStatItem(
          theme,
          icon: lucide.LucideIcons.shield,
          label: 'Status',
          value: adminUser.isActive ? 'Active' : 'Inactive',
        ),
      ],
    );
  }

  Widget _buildStatItem(
    ShadThemeData theme, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.small.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildProfileTab(ShadThemeData theme, AdminUser adminUser) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: ShadForm(
        key: _profileFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Profile Information',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Update your account information and personal details.',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
            const SizedBox(height: 24),

            // Display Name
            ShadInputFormField(
              id: 'display_name',
              controller: _displayNameController,
              label: const Text('Display Name'),
              placeholder: const Text('Enter your display name'),
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.user, size: 16),
              ),
              validator: (value) {
                if (value.isEmpty) {
                  return 'Display name is required';
                }
                if (value.length < 2) {
                  return 'Display name must be at least 2 characters';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email (read-only)
            ShadInputFormField(
              id: 'email',
              controller: _emailController,
              label: const Text('Email Address'),
              placeholder: const Text('Your email address'),
              enabled: false,
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.mail, size: 16),
              ),
            ),
            const SizedBox(height: 16),

            // Phone Number
            ShadInputFormField(
              id: 'phone',
              controller: _phoneController,
              label: const Text('Phone Number'),
              placeholder: const Text('Enter your phone number'),
              keyboardType: TextInputType.phone,
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.phone, size: 16),
              ),
              validator: (value) {
                if (value.isNotEmpty && value.length < 10) {
                  return 'Please enter a valid phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 32),

            // Update Profile Button
            ShadButton(
              onPressed: _isUpdatingProfile ? null : _updateProfile,
              child:
                  _isUpdatingProfile
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Update Profile'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: ShadForm(
        key: _passwordFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Change Password',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Update your password to keep your account secure.',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
            const SizedBox(height: 24),

            // Current Password
            ShadInputFormField(
              id: 'current_password',
              controller: _currentPasswordController,
              label: const Text('Current Password'),
              placeholder: const Text('Enter your current password'),
              obscureText: _obscureCurrentPassword,
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.lock, size: 16),
              ),
              trailing: ShadIconButton.ghost(
                icon: Icon(
                  _obscureCurrentPassword
                      ? lucide.LucideIcons.eyeOff
                      : lucide.LucideIcons.eye,
                  size: 16,
                ),
                onPressed: () {
                  setState(() {
                    _obscureCurrentPassword = !_obscureCurrentPassword;
                  });
                },
              ),
              validator: (value) {
                if (value.isEmpty) {
                  return 'Current password is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // New Password
            ShadInputFormField(
              id: 'new_password',
              controller: _newPasswordController,
              label: const Text('New Password'),
              placeholder: const Text('Enter your new password'),
              obscureText: _obscureNewPassword,
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.lock, size: 16),
              ),
              trailing: ShadIconButton.ghost(
                icon: Icon(
                  _obscureNewPassword
                      ? lucide.LucideIcons.eyeOff
                      : lucide.LucideIcons.eye,
                  size: 16,
                ),
                onPressed: () {
                  setState(() {
                    _obscureNewPassword = !_obscureNewPassword;
                  });
                },
              ),
              validator: (value) {
                if (value.isEmpty) {
                  return 'New password is required';
                }
                if (value.length < 8) {
                  return 'Password must be at least 8 characters';
                }
                if (!RegExp(
                  r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)',
                ).hasMatch(value)) {
                  return 'Password must contain uppercase, lowercase, and number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Confirm Password
            ShadInputFormField(
              id: 'confirm_password',
              controller: _confirmPasswordController,
              label: const Text('Confirm New Password'),
              placeholder: const Text('Confirm your new password'),
              obscureText: _obscureConfirmPassword,
              leading: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(lucide.LucideIcons.lock, size: 16),
              ),
              trailing: ShadIconButton.ghost(
                icon: Icon(
                  _obscureConfirmPassword
                      ? lucide.LucideIcons.eyeOff
                      : lucide.LucideIcons.eye,
                  size: 16,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
              validator: (value) {
                if (value.isEmpty) {
                  return 'Please confirm your new password';
                }
                if (value != _newPasswordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
            ),
            const SizedBox(height: 32),

            // Change Password Button
            ShadButton(
              onPressed: _isChangingPassword ? null : _changePassword,
              child:
                  _isChangingPassword
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Admin Settings',
            style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Configure your admin preferences and system settings.',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 24),

          // Notification Settings
          _buildSettingsSection(
            theme,
            title: 'Notifications',
            icon: lucide.LucideIcons.bell,
            children: [
              _buildSettingItem(
                theme,
                title: 'Email Notifications',
                subtitle: 'Receive email notifications for important events',
                trailing: ShadSwitch(
                  value: true,
                  onChanged: (value) {
                    // TODO: Implement notification settings
                    LoggerService.info('Email notifications toggled: $value');
                  },
                ),
              ),
              _buildSettingItem(
                theme,
                title: 'Push Notifications',
                subtitle: 'Receive push notifications on your device',
                trailing: ShadSwitch(
                  value: false,
                  onChanged: (value) {
                    // TODO: Implement notification settings
                    LoggerService.info('Push notifications toggled: $value');
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Security Settings
          _buildSettingsSection(
            theme,
            title: 'Security',
            icon: lucide.LucideIcons.shield,
            children: [
              _buildSettingItem(
                theme,
                title: 'Two-Factor Authentication',
                subtitle: 'Add an extra layer of security to your account',
                trailing: ShadButton.outline(
                  size: ShadButtonSize.sm,
                  onPressed: () {
                    // TODO: Implement 2FA setup
                    LoggerService.info('2FA setup requested');
                  },
                  child: const Text('Setup'),
                ),
              ),
              _buildSettingItem(
                theme,
                title: 'Session Timeout',
                subtitle:
                    'Automatically sign out after 30 minutes of inactivity',
                trailing: ShadSwitch(
                  value: true,
                  onChanged: (value) {
                    // TODO: Implement session timeout settings
                    LoggerService.info('Session timeout toggled: $value');
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // System Settings
          _buildSettingsSection(
            theme,
            title: 'System',
            icon: lucide.LucideIcons.settings,
            children: [
              _buildSettingItem(
                theme,
                title: 'Real-time Updates',
                subtitle: 'Enable real-time data synchronization',
                trailing: Consumer(
                  builder: (context, ref, child) {
                    final realtimeState = ref.watch(realtimeUpdateProvider);
                    return ShadSwitch(
                      value: realtimeState.isConnected,
                      onChanged: (value) {
                        if (value) {
                          // Trigger reconnection if not connected
                          ref.read(realtimeUpdateProvider.notifier).reconnect();
                        } else {
                          // Note: Cannot manually disconnect, connection is auto-managed
                          LoggerService.info(
                            'Real-time updates cannot be manually disabled',
                          );
                        }
                      },
                    );
                  },
                ),
              ),
              _buildSettingItem(
                theme,
                title: 'Debug Mode',
                subtitle: 'Enable detailed logging for troubleshooting',
                trailing: ShadSwitch(
                  value: false,
                  onChanged: (value) {
                    // TODO: Implement debug mode settings
                    LoggerService.info('Debug mode toggled: $value');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    ShadThemeData theme, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    ShadThemeData theme, {
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ),
          ),
          trailing,
        ],
      ),
    );
  }

  Future<void> _updateProfile() async {
    if (!_profileFormKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isUpdatingProfile = true;
    });

    try {
      // TODO: Implement profile update logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      LoggerService.info('Profile updated successfully');

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(description: Text('Profile updated successfully')),
        );
      }
    } catch (e) {
      LoggerService.error('Failed to update profile', e);

      if (mounted) {
        ShadToaster.of(
          context,
        ).show(ShadToast(description: Text('Failed to update profile: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingProfile = false;
        });
      }
    }
  }

  Future<void> _changePassword() async {
    if (!_passwordFormKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isChangingPassword = true;
    });

    try {
      // TODO: Implement password change logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      LoggerService.info('Password changed successfully');

      // Clear password fields
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(description: Text('Password changed successfully')),
        );
      }
    } catch (e) {
      LoggerService.error('Failed to change password', e);

      if (mounted) {
        ShadToaster.of(
          context,
        ).show(ShadToast(description: Text('Failed to change password: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChangingPassword = false;
        });
      }
    }
  }
}
