import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Admin system settings page
class AdminSettingsPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/settings';

  const AdminSettingsPage({super.key});

  @override
  ConsumerState<AdminSettingsPage> createState() => _AdminSettingsPageState();
}

class _AdminSettingsPageState extends ConsumerState<AdminSettingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Form controllers for various settings
  final _systemNameController = TextEditingController();
  final _systemEmailController = TextEditingController();
  final _smtpHostController = TextEditingController();
  final _smtpPortController = TextEditingController();
  final _smtpUsernameController = TextEditingController();
  final _smtpPasswordController = TextEditingController();

  // Settings state
  bool _maintenanceMode = false;
  bool _userRegistrationEnabled = true;
  bool _emailNotificationsEnabled = true;
  bool _smsNotificationsEnabled = false;
  bool _autoBackupEnabled = true;
  bool _realTimeUpdatesEnabled = true;
  bool _auditLoggingEnabled = true;
  bool _smtpTlsEnabled = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _initializeSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _systemNameController.dispose();
    _systemEmailController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    _smtpUsernameController.dispose();
    _smtpPasswordController.dispose();
    super.dispose();
  }

  void _initializeSettings() {
    // Initialize with default values
    _systemNameController.text = '3Pay Global Platform';
    _systemEmailController.text = '<EMAIL>';
    _smtpHostController.text = 'smtp.gmail.com';
    _smtpPortController.text = '587';
    _smtpUsernameController.text = '<EMAIL>';
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(adminAuthProvider);
    final adminUser = authState.adminUser;

    // Check permissions
    if (adminUser == null || !adminUser.canManageSettings) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                lucide.LucideIcons.lock,
                size: 64,
                color: theme.colorScheme.muted,
              ),
              const SizedBox(height: 16),
              Text('Access Denied', style: theme.textTheme.h3),
              const SizedBox(height: 8),
              Text(
                'You do not have permission to access system settings.',
                style: theme.textTheme.muted,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: Column(
        children: [
          _buildHeader(theme),
          _buildTabBar(theme),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSystemTab(theme),
                _buildSecurityTab(theme),
                _buildEmailTab(theme),
                _buildNotificationsTab(theme),
                _buildMaintenanceTab(theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    return Container(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: Row(
        children: [
          Icon(
            lucide.LucideIcons.settings,
            size: 24,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'System Settings',
                  style: theme.textTheme.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Configure system-wide settings and preferences',
                  style: theme.textTheme.muted,
                ),
              ],
            ),
          ),
          ShadButton(
            onPressed: _saveAllSettings,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(lucide.LucideIcons.save, size: 16),
                SizedBox(width: 8),
                Text('Save All'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(ShadThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: theme.colorScheme.primary,
        unselectedLabelColor: theme.colorScheme.mutedForeground,
        indicatorColor: theme.colorScheme.primary,
        tabs: const [
          Tab(
            icon: Icon(lucide.LucideIcons.settings, size: 16),
            text: 'System',
          ),
          Tab(
            icon: Icon(lucide.LucideIcons.shield, size: 16),
            text: 'Security',
          ),
          Tab(icon: Icon(lucide.LucideIcons.mail, size: 16), text: 'Email'),
          Tab(
            icon: Icon(lucide.LucideIcons.bell, size: 16),
            text: 'Notifications',
          ),
          Tab(
            icon: Icon(lucide.LucideIcons.wrench, size: 16),
            text: 'Maintenance',
          ),
        ],
      ),
    );
  }

  Widget _buildSystemTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection(
            theme,
            title: 'General Configuration',
            icon: lucide.LucideIcons.settings,
            children: [
              ShadInputFormField(
                controller: _systemNameController,
                label: const Text('System Name'),
                placeholder: const Text('Enter system name'),
                description: const Text(
                  'The name displayed across the platform',
                ),
              ),
              const SizedBox(height: 16),
              ShadInputFormField(
                controller: _systemEmailController,
                label: const Text('System Email'),
                placeholder: const Text('Enter system email address'),
                description: const Text(
                  'Default email for system notifications',
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildSettingsSection(
            theme,
            title: 'User Management',
            icon: lucide.LucideIcons.users,
            children: [
              _buildSettingToggle(
                theme,
                title: 'User Registration',
                subtitle: 'Allow new users to register on the platform',
                value: _userRegistrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _userRegistrationEnabled = value;
                  });
                },
              ),
              _buildSettingToggle(
                theme,
                title: 'Real-time Updates',
                subtitle: 'Enable real-time data synchronization',
                value: _realTimeUpdatesEnabled,
                onChanged: (value) {
                  setState(() {
                    _realTimeUpdatesEnabled = value;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection(
            theme,
            title: 'Security Configuration',
            icon: lucide.LucideIcons.shield,
            children: [
              _buildSettingToggle(
                theme,
                title: 'Audit Logging',
                subtitle: 'Log all administrative actions and user activities',
                value: _auditLoggingEnabled,
                onChanged: (value) {
                  setState(() {
                    _auditLoggingEnabled = value;
                  });
                },
              ),
              _buildSettingItem(
                theme,
                title: 'Session Timeout',
                subtitle: 'Automatically sign out inactive users',
                trailing: ShadSelect<String>(
                  placeholder: const Text('Select timeout'),
                  options: [
                    ShadOption(value: '15', child: Text('15 minutes')),
                    ShadOption(value: '30', child: Text('30 minutes')),
                    ShadOption(value: '60', child: Text('1 hour')),
                    ShadOption(value: '120', child: Text('2 hours')),
                  ],
                  selectedOptionBuilder:
                      (context, value) => Text('$value minutes'),
                  onChanged: (value) {
                    LoggerService.info(
                      'Session timeout changed to: $value minutes',
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmailTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection(
            theme,
            title: 'SMTP Configuration',
            icon: lucide.LucideIcons.mail,
            children: [
              ShadInputFormField(
                controller: _smtpHostController,
                label: const Text('SMTP Host'),
                placeholder: const Text('smtp.gmail.com'),
              ),
              const SizedBox(height: 16),
              ShadInputFormField(
                controller: _smtpPortController,
                label: const Text('SMTP Port'),
                placeholder: const Text('587'),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              ShadInputFormField(
                controller: _smtpUsernameController,
                label: const Text('SMTP Username'),
                placeholder: const Text('Enter SMTP username'),
              ),
              const SizedBox(height: 16),
              ShadInputFormField(
                controller: _smtpPasswordController,
                label: const Text('SMTP Password'),
                placeholder: const Text('Enter SMTP password'),
                obscureText: true,
              ),
              const SizedBox(height: 16),
              _buildSettingToggle(
                theme,
                title: 'Enable TLS',
                subtitle: 'Use TLS encryption for SMTP connection',
                value: _smtpTlsEnabled,
                onChanged: (value) {
                  setState(() {
                    _smtpTlsEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              ShadButton.outline(
                onPressed: _testEmailConfiguration,
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(lucide.LucideIcons.send, size: 16),
                    SizedBox(width: 8),
                    Text('Test Email'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection(
            theme,
            title: 'Notification Settings',
            icon: lucide.LucideIcons.bell,
            children: [
              _buildSettingToggle(
                theme,
                title: 'Email Notifications',
                subtitle: 'Send email notifications for system events',
                value: _emailNotificationsEnabled,
                onChanged: (value) {
                  setState(() {
                    _emailNotificationsEnabled = value;
                  });
                },
              ),
              _buildSettingToggle(
                theme,
                title: 'SMS Notifications',
                subtitle: 'Send SMS notifications for critical events',
                value: _smsNotificationsEnabled,
                onChanged: (value) {
                  setState(() {
                    _smsNotificationsEnabled = value;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTab(ShadThemeData theme) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsSection(
            theme,
            title: 'System Maintenance',
            icon: lucide.LucideIcons.wrench,
            children: [
              _buildSettingToggle(
                theme,
                title: 'Maintenance Mode',
                subtitle: 'Put the system in maintenance mode',
                value: _maintenanceMode,
                onChanged: (value) {
                  setState(() {
                    _maintenanceMode = value;
                  });
                },
              ),
              _buildSettingToggle(
                theme,
                title: 'Auto Backup',
                subtitle: 'Automatically backup system data daily',
                value: _autoBackupEnabled,
                onChanged: (value) {
                  setState(() {
                    _autoBackupEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              ShadButton.outline(
                onPressed: _performBackup,
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(lucide.LucideIcons.download, size: 16),
                    SizedBox(width: 8),
                    Text('Backup Now'),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              ShadButton.outline(
                onPressed: _clearCache,
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(lucide.LucideIcons.trash2, size: 16),
                    SizedBox(width: 8),
                    Text('Clear Cache'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods
  Widget _buildSettingsSection(
    ShadThemeData theme, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSettingToggle(
    ShadThemeData theme, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return _buildSettingItem(
      theme,
      title: title,
      subtitle: subtitle,
      trailing: ShadSwitch(value: value, onChanged: onChanged),
    );
  }

  Widget _buildSettingItem(
    ShadThemeData theme, {
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.small.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.muted.copyWith(fontSize: 12),
                ),
              ],
            ),
          ),
          trailing,
        ],
      ),
    );
  }

  void _saveAllSettings() {
    // TODO: Implement settings save functionality
    LoggerService.info('Saving all system settings');

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Settings saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _testEmailConfiguration() {
    // TODO: Implement email test functionality
    LoggerService.info('Testing email configuration');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test email sent successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _performBackup() {
    // TODO: Implement backup functionality
    LoggerService.info('Performing system backup');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Backup completed successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _clearCache() {
    // TODO: Implement cache clearing functionality
    LoggerService.info('Clearing system cache');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cache cleared successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
