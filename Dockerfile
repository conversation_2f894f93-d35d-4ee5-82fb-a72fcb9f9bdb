# Use official Flutter Docker image with latest stable version
FROM ghcr.io/cirruslabs/flutter:3.32.4 AS build-env

# Configure Flutter for web and disable analytics`
RUN flutter config --no-analytics && \
    flutter config --enable-web && \
    flutter doctor -v

# Set working directory
WORKDIR /usr/src/app

# Copy pubspec files first for better Docker layer caching
COPY pubspec.yaml ./
COPY pubspec.lock* ./

# Get dependencies (this layer will be cached if pubspec files don't change)
RUN flutter pub get

# Copy source code and assets
COPY lib/ lib/
COPY web/ web/
COPY assets/ assets/

# Copy analysis_options.yaml if it exists
COPY analysis_options.yaml* ./

# Copy additional configuration files if they exist
COPY dotenv* ./

# Verify Flutter installation and dependencies
RUN flutter doctor -v && flutter pub deps

# Build the Flutter web app with optimizations for better compatibility
RUN flutter build web \
    --release \
    --dart-define=FLUTTER_WEB_USE_SKIA=false \
    --dart-define=FLUTTER_WEB_AUTO_DETECT=false \
    --dart-define=FLUTTER_WEB_CANVASKIT_URL=https://unpkg.com/canvaskit-wasm@0.39.1/bin/ \
    --verbose

# Copy environment files to build output (if they exist)
RUN if [ -f dotenv ]; then \
        cp dotenv /usr/src/app/build/web/ && \
        echo "Copied dotenv file to build output"; \
    fi

# Copy nginx.conf to build directory if it exists
RUN if [ -f /usr/src/app/web/nginx.conf ]; then \
    cp /usr/src/app/web/nginx.conf /usr/src/app/build/web/nginx.conf; \
    echo "Found and copied nginx.conf"; \
fi

# Create a lightweight production image with Python
FROM python:3.12-alpine

# Install security updates and clean up
RUN apk update && \
    apk upgrade && \
    apk add --no-cache ca-certificates curl && \
    rm -rf /var/cache/apk/*

# Create a non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Create app directory
WORKDIR /app

# Copy the built app to the app directory
COPY --from=build-env /usr/src/app/build/web /app

# Set proper permissions
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Add metadata
LABEL maintainer="3Pay Global Team" \
      description="3Pay Global Flutter Web Application" \
      version="1.0.0"

# Expose the web server port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start Python HTTP server
CMD ["python3", "-m", "http.server", "80"]
